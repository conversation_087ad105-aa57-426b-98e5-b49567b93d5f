'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFingerprint, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { useTheme } from '@/lib/context/ThemeContext';
import { ParallaxBackground } from '@/components/ui/ParallaxBackground';
import { GlassmorphicCard } from '@/components/ui/GlassmorphicCard';
import { CrefyButton } from '@/components/ui/CrefyButton';
import { AnimatedScrollContainer } from '@/components/ui/AnimatedScrollContainer';
import { ConnectButton } from 'thirdweb/react';
import { client } from '@/config/client';
import { useProfiles } from "thirdweb/react";
import { useActiveAccount } from 'thirdweb/react';
import { authService } from '@/lib/api/useAuth';
import { signLoginPayload } from 'thirdweb/auth';
import { generatePayload, verifyPayload } from '@/lib/api/auth';
import customWallets from '@/config/connect-widget';

export default function LoginPage() {
    const { theme } = useTheme();
    const router = useRouter();
    const [authStatus, setAuthStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
    const [authError, setAuthError] = useState('');
    const account = useActiveAccount();
    const address = account?.address;

    // Get account and profiles at the top level
    const { data: profiles } = useProfiles({
        client
    });

    useEffect(() => {
        if (address) {
            if (!localStorage.getItem('walletAddress')) {
                localStorage.setItem('walletAddress', address);
            }
            setAuthStatus('loading');
            handleAuth();
        }
    }, [address]);

    const handleAuth = async () => {
        try {
            if (!profiles && !address) return;
            console.log(address);
            setAuthStatus('loading');

            const nonce = await authService.getNonce();
            console.log('Got nonce:', nonce);

            if (!nonce) {
                setAuthStatus('error');
                setAuthError('Failed to get nonce');
                return;
            }

            const message = `I am signing this message to authenticate with Crefy. Nonce: ${nonce}`;
            if (!account) {
                throw new Error('Account is undefined');
            }

            const payload = await generatePayload({
                address: account?.address,
                chainId: 17000,
            });

            const signatureResponse = await signLoginPayload({
                payload,
                account
            });
            console.log('signature', signatureResponse.signature);

            const finalResult = await verifyPayload(signatureResponse);
            console.log('finalResult', finalResult);

            // Send to server for verification
            const authResponse = await authService.login(message, signatureResponse, address as any);

            setAuthStatus('success');

            // Redirect after short delay to show success state
            setTimeout(() => {
                router.push('/dashboard');
            }, 2000);

        } catch (error) {
            console.error('Authentication error:', error);
            setAuthStatus('error');
            setAuthError(error instanceof Error ? error.message : 'Authentication failed');
        }
    };

    const handleBackToHome = () => {
        router.push('/');
    };

    return (
        <div className="min-h-screen">
            <ParallaxBackground orbCount={10}>
                {/* Enhanced interactive floating elements for login page */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    {Array.from({ length: 6 }).map((_, i) => (
                        <motion.div
                            key={`login-orb-${i}`}
                            className="absolute rounded-full"
                            style={{
                                width: `${Math.random() * 150 + 80}px`,
                                height: `${Math.random() * 150 + 80}px`,
                                background: `radial-gradient(circle, ${theme.colors.primary}35 0%, ${theme.colors.accent}20 50%, transparent 80%)`,
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                                filter: 'blur(40px)',
                            }}
                            animate={{
                                x: [0, Math.random() * 150 - 75, Math.random() * 100 - 50, 0],
                                y: [0, Math.random() * 150 - 75, Math.random() * 100 - 50, 0],
                                scale: [1, 1.3, 0.8, 1],
                                opacity: [0.15, 0.4, 0.25, 0.15],
                                rotate: [0, 180, 360],
                            }}
                            transition={{
                                duration: Math.random() * 15 + 20,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: Math.random() * 5,
                            }}
                        >
                            {/* Inner pulsing core */}
                            <motion.div
                                className="absolute inset-4 rounded-full"
                                style={{
                                    background: `radial-gradient(circle, ${theme.colors.primary}50, transparent)`,
                                    filter: 'blur(20px)',
                                }}
                                animate={{
                                    scale: [0.5, 1.2, 0.5],
                                    opacity: [0.3, 0.8, 0.3],
                                }}
                                transition={{
                                    duration: Math.random() * 8 + 6,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                    delay: Math.random() * 2,
                                }}
                            />
                        </motion.div>
                    ))}

                    {/* Floating sparkles */}
                    {Array.from({ length: 8 }).map((_, i) => (
                        <motion.div
                            key={`sparkle-${i}`}
                            className="absolute w-2 h-2 rounded-full"
                            style={{
                                background: theme.colors.primary,
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                                boxShadow: `0 0 10px ${theme.colors.primary}`,
                            }}
                            animate={{
                                y: [0, -30, 0],
                                x: [0, Math.random() * 20 - 10, 0],
                                opacity: [0, 1, 0],
                                scale: [0, 1, 0],
                            }}
                            transition={{
                                duration: Math.random() * 4 + 3,
                                repeat: Infinity,
                                ease: "easeOut",
                                delay: Math.random() * 3,
                            }}
                        />
                    ))}
                </div>

                <section className="min-h-screen flex items-center justify-center px-4">
                    <div className="max-w-md mx-auto w-full">
                        <AnimatedScrollContainer animation="slideUp">
                            <GlassmorphicCard
                                padding="xl"
                                className="text-center"
                                border={false}
                                rounded="3xl"
                                style={{
                                    backgroundColor: 'rgba(255, 255, 255, 0.08)',
                                    backdropFilter: 'blur(30px)',
                                    boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',
                                }}
                            >
                                {/* Back Button */}
                                <div className="flex justify-start mb-6">
                                    <CrefyButton
                                        variant="glass"
                                        size="sm"
                                        onClick={handleBackToHome}
                                    >
                                        <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
                                        Back to Home
                                    </CrefyButton>
                                </div>

                                {/* Enhanced Auth Icon */}
                                <motion.div
                                    className="mb-8"
                                    initial={{ scale: 0.3, opacity: 0, y: 50 }}
                                    animate={{
                                        scale: authStatus === 'loading' ? [1, 1.15, 1] : 1,
                                        opacity: 1,
                                        y: 0,
                                        transition: authStatus === 'loading' ?
                                            { repeat: Infinity, duration: 2, ease: "easeInOut" } :
                                            { duration: 0.8, ease: "backOut" }
                                    }}
                                >
                                    <motion.div
                                        className="w-24 h-24 mx-auto mb-6 rounded-3xl flex items-center justify-center relative overflow-hidden"
                                        style={{
                                            backgroundColor:
                                                authStatus === 'success' ? '#4CAF50' :
                                                authStatus === 'error' ? '#F44336' :
                                                theme.colors.primary,
                                            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.2)',
                                        }}
                                        whileHover={{
                                            scale: 1.1,
                                            rotate: 5,
                                            boxShadow: '0 15px 50px rgba(0, 0, 0, 0.3)',
                                        }}
                                        whileTap={{ scale: 0.9, rotate: -5 }}
                                    >
                                        {/* Multiple animated rings */}
                                        <motion.div
                                            className="absolute inset-0 rounded-3xl border-2"
                                            style={{ borderColor: theme.colors.primary }}
                                            animate={{
                                                scale: [1, 1.3, 1],
                                                opacity: [0.6, 0, 0.6],
                                                rotate: [0, 180, 360],
                                            }}
                                            transition={{
                                                duration: 3,
                                                repeat: Infinity,
                                                ease: "easeInOut",
                                            }}
                                        />
                                        <motion.div
                                            className="absolute inset-0 rounded-3xl border border-white"
                                            animate={{
                                                scale: [1, 1.2, 1],
                                                opacity: [0.3, 0, 0.3],
                                                rotate: [360, 180, 0],
                                            }}
                                            transition={{
                                                duration: 2.5,
                                                repeat: Infinity,
                                                ease: "easeInOut",
                                                delay: 0.5,
                                            }}
                                        />

                                        {/* Floating particles */}
                                        {Array.from({ length: 3 }).map((_, i) => (
                                            <motion.div
                                                key={i}
                                                className="absolute w-1 h-1 bg-white rounded-full"
                                                style={{
                                                    left: `${20 + i * 20}%`,
                                                    top: `${20 + i * 15}%`,
                                                }}
                                                animate={{
                                                    y: [-10, 10, -10],
                                                    x: [-5, 5, -5],
                                                    opacity: [0.3, 1, 0.3],
                                                }}
                                                transition={{
                                                    duration: 2 + i * 0.5,
                                                    repeat: Infinity,
                                                    ease: "easeInOut",
                                                    delay: i * 0.3,
                                                }}
                                            />
                                        ))}

                                        <motion.div
                                            animate={{
                                                rotate: authStatus === 'loading' ? [0, 360] : 0,
                                            }}
                                            transition={{
                                                duration: 2,
                                                repeat: authStatus === 'loading' ? Infinity : 0,
                                                ease: "linear",
                                            }}
                                        >
                                            <FontAwesomeIcon
                                                icon={faFingerprint}
                                                size="2x"
                                                className="text-white"
                                            />
                                        </motion.div>
                                    </motion.div>
                                </motion.div>

                                {/* Title */}
                                <h1
                                    className="text-3xl font-bold mb-4"
                                    style={{ color: theme.colors.text }}
                                >
                                    {authStatus === 'idle' && 'Connect Your Wallet'}
                                    {authStatus === 'loading' && 'Authenticating...'}
                                    {authStatus === 'success' && 'Welcome to Crefy!'}
                                    {authStatus === 'error' && 'Authentication Failed'}
                                </h1>

                                {/* Description */}
                                <p
                                    className="text-lg mb-8"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    {authStatus === 'idle' && 'Connect your wallet to access our partner companies and experience secure, decentralized identity verification.'}
                                    {authStatus === 'loading' && 'Verifying your identity using zero-knowledge proofs...'}
                                    {authStatus === 'success' && 'Authentication successful! Redirecting to your dashboard...'}
                                    {authStatus === 'error' && authError}
                                </p>

                                {/* Auth Content */}
                                <AnimatePresence mode="wait">
                                    {authStatus === 'idle' && (
                                        <motion.div
                                            key="idle"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                        >
                                            <ConnectButton
                                                client={client}
                                                wallets={customWallets}
                                                connectModal={{ size: "compact" }}
                                                connectButton={{
                                                    label: "Connect Wallet",
                                                    style: {
                                                        background: theme.colors.primary,
                                                        border: 'none',
                                                        borderRadius: '12px',
                                                        padding: '16px 32px',
                                                        color: 'white',
                                                        fontSize: '1.1rem',
                                                        fontWeight: '600',
                                                        width: '100%',
                                                        minHeight: '56px'
                                                    }
                                                }}
                                                theme='light'
                                                appMetadata={{
                                                    name: "Crefy",
                                                    url: "https://crefy.xyz",
                                                }}
                                            />
                                        </motion.div>
                                    )}

                                    {authStatus === 'loading' && (
                                        <motion.div
                                            key="loading"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                            className="space-y-4"
                                        >
                                            <div className="w-full bg-gray-200 rounded-full h-3">
                                                <div
                                                    className="h-3 rounded-full transition-all duration-1000"
                                                    style={{
                                                        backgroundColor: theme.colors.primary,
                                                        width: '70%',
                                                        animation: 'pulse 2s infinite'
                                                    }}
                                                />
                                            </div>
                                            <p
                                                className="text-sm"
                                                style={{ color: theme.colors.secondaryText }}
                                            >
                                                Please confirm the transaction in your wallet...
                                            </p>
                                        </motion.div>
                                    )}

                                    {authStatus === 'success' && (
                                        <motion.div
                                            key="success"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                            className="text-center"
                                        >
                                            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                                            <p
                                                className="text-sm"
                                                style={{ color: theme.colors.secondaryText }}
                                            >
                                                Redirecting to dashboard...
                                            </p>
                                        </motion.div>
                                    )}

                                    {authStatus === 'error' && (
                                        <motion.div
                                            key="error"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                            className="space-y-4"
                                        >
                                            <CrefyButton
                                                variant="primary"
                                                size="lg"
                                                fullWidth
                                                onClick={() => setAuthStatus('idle')}
                                            >
                                                Try Again
                                            </CrefyButton>
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </GlassmorphicCard>
                        </AnimatedScrollContainer>
                    </div>
                </section>
            </ParallaxBackground>
        </div>
    );
}
