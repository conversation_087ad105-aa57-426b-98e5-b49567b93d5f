'use client';

import React, { useEffect, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useTheme } from '@/lib/context/ThemeContext';

interface FloatingOrb {
    id: number;
    x: number;
    y: number;
    size: number;
    color: string;
    opacity: number;
    duration: number;
    delay: number;
    rotationSpeed: number;
    pulseSpeed: number;
}

interface CrefyFloatingOrbsProps {
    orbCount?: number;
    className?: string;
    variant?: 'subtle' | 'normal' | 'intense';
}

export const CrefyFloatingOrbs: React.FC<CrefyFloatingOrbsProps> = ({
    orbCount = 8,
    className = '',
    variant = 'normal'
}) => {
    const { theme } = useTheme();
    const orbsRef = useRef<FloatingOrb[]>([]);

    // Variant configurations
    const variantConfig = {
        subtle: {
            sizeRange: [100, 200],
            opacityRange: [0.05, 0.15],
            durationRange: [30, 50],
            blurAmount: 50
        },
        normal: {
            sizeRange: [150, 300],
            opacityRange: [0.1, 0.3],
            durationRange: [25, 40],
            blurAmount: 60
        },
        intense: {
            sizeRange: [200, 400],
            opacityRange: [0.15, 0.4],
            durationRange: [20, 35],
            blurAmount: 70
        }
    };

    const config = variantConfig[variant];

    // Generate enhanced floating orbs
    useEffect(() => {
        const colors = [
            theme.colors.primary,
            theme.colors.accent,
            theme.colors.accentLight,
            theme.colors.secondary,
        ];

        orbsRef.current = Array.from({ length: orbCount }, (_, i) => ({
            id: i,
            x: Math.random() * 100,
            y: Math.random() * 100,
            size: Math.random() * (config.sizeRange[1] - config.sizeRange[0]) + config.sizeRange[0],
            color: colors[Math.floor(Math.random() * colors.length)],
            opacity: Math.random() * (config.opacityRange[1] - config.opacityRange[0]) + config.opacityRange[0],
            duration: Math.random() * (config.durationRange[1] - config.durationRange[0]) + config.durationRange[0],
            delay: Math.random() * 10,
            rotationSpeed: Math.random() * 20 + 15,
            pulseSpeed: Math.random() * 8 + 6,
        }));
    }, [theme, orbCount, config]);

    const FloatingOrb: React.FC<{ orb: FloatingOrb }> = ({ orb }) => {
        const controls = useAnimation();
        const rotationControls = useAnimation();
        const pulseControls = useAnimation();

        useEffect(() => {
            const animateOrb = async () => {
                // Main floating movement with organic patterns
                controls.start({
                    x: [0, Math.random() * 200 - 100, Math.random() * 150 - 75, 0],
                    y: [0, Math.random() * 200 - 100, Math.random() * 150 - 75, 0],
                    scale: [1, Math.random() * 0.5 + 0.8, Math.random() * 0.4 + 1.0, 1],
                    transition: {
                        duration: orb.duration,
                        ease: "easeInOut",
                        repeat: Infinity,
                        delay: orb.delay,
                    }
                });

                // Subtle rotation animation
                rotationControls.start({
                    rotate: [0, 360],
                    transition: {
                        duration: orb.rotationSpeed,
                        ease: "linear",
                        repeat: Infinity,
                    }
                });

                // Gentle pulsing effect
                pulseControls.start({
                    opacity: [orb.opacity, orb.opacity * 1.8, orb.opacity],
                    scale: [1, 1.15, 1],
                    transition: {
                        duration: orb.pulseSpeed,
                        ease: "easeInOut",
                        repeat: Infinity,
                    }
                });
            };

            animateOrb();
        }, [controls, rotationControls, pulseControls, orb]);

        return (
            <motion.div
                className="absolute rounded-full"
                style={{
                    left: `${orb.x}%`,
                    top: `${orb.y}%`,
                    width: `${orb.size}px`,
                    height: `${orb.size}px`,
                    background: `radial-gradient(circle, ${orb.color}40 0%, ${orb.color}20 40%, transparent 70%)`,
                    filter: `blur(${config.blurAmount}px)`,
                }}
                animate={controls}
                initial={{ opacity: 0, scale: 0.3 }}
                whileInView={{ opacity: orb.opacity, scale: 1 }}
                transition={{ duration: 4, ease: "easeOut" }}
            >
                {/* Inner glow effect */}
                <motion.div
                    className="absolute inset-0 rounded-full"
                    style={{
                        background: `radial-gradient(circle, ${orb.color}30 0%, transparent 60%)`,
                        filter: `blur(${config.blurAmount * 0.5}px)`,
                    }}
                    animate={pulseControls}
                />
                
                {/* Outer subtle ring */}
                <motion.div
                    className="absolute inset-0 rounded-full"
                    style={{
                        background: `conic-gradient(from 0deg, ${orb.color}25, transparent, ${orb.color}15)`,
                        filter: `blur(${config.blurAmount * 0.7}px)`,
                    }}
                    animate={rotationControls}
                />
            </motion.div>
        );
    };

    return (
        <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
            {orbsRef.current.map((orb) => (
                <FloatingOrb key={orb.id} orb={orb} />
            ))}
        </div>
    );
};
