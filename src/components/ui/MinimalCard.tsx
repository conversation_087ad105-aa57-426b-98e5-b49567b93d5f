'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/lib/context/ThemeContext';

interface MinimalCardProps {
    children: React.ReactNode;
    className?: string;
    variant?: 'default' | 'hover' | 'minimal' | 'bordered';
    padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
    onClick?: () => void;
    style?: React.CSSProperties;
    hoverScale?: number;
    delay?: number;
}

export const MinimalCard: React.FC<MinimalCardProps> = ({
    children,
    className = '',
    variant = 'default',
    padding = 'md',
    onClick,
    style = {},
    hoverScale = 1.02,
    delay = 0
}) => {
    const { theme } = useTheme();

    const paddingClasses = {
        none: '',
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
        xl: 'p-12'
    };

    const getVariantStyles = () => {
        switch (variant) {
            case 'minimal':
                return {
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none'
                };
            case 'bordered':
                return {
                    backgroundColor: theme.colors.background,
                    border: `1px solid ${theme.colors.border}`,
                    boxShadow: 'none'
                };
            case 'hover':
                return {
                    backgroundColor: theme.colors.background,
                    border: `1px solid transparent`,
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.3s ease'
                };
            default:
                return {
                    backgroundColor: theme.colors.background,
                    border: 'none',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                };
        }
    };

    const baseClasses = `
        ${paddingClasses[padding]}
        rounded-lg
        ${onClick ? 'cursor-pointer' : ''}
        ${className}
    `.trim().replace(/\s+/g, ' ');

    const cardStyle = {
        ...getVariantStyles(),
        ...style
    };

    return (
        <motion.div
            className={baseClasses}
            style={cardStyle}
            onClick={onClick}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay, ease: "easeOut" }}
            whileHover={onClick ? { 
                scale: hoverScale,
                boxShadow: variant === 'hover' ? '0 8px 25px rgba(0, 0, 0, 0.15)' : undefined,
                borderColor: variant === 'bordered' ? theme.colors.primary : undefined
            } : {}}
            whileTap={onClick ? { scale: 0.98 } : {}}
        >
            {children}
        </motion.div>
    );
};

// Project card component inspired by SOHub design
interface ProjectCardProps {
    title: string;
    description: string;
    icon?: React.ReactNode;
    features?: string[];
    onClick?: () => void;
    delay?: number;
    accentColor?: string;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({
    title,
    description,
    icon,
    features = [],
    onClick,
    delay = 0,
    accentColor
}) => {
    const { theme } = useTheme();

    return (
        <MinimalCard
            variant="hover"
            padding="lg"
            onClick={onClick}
            delay={delay}
            className="h-full group"
        >
            <div className="flex flex-col h-full">
                {icon && (
                    <div className="mb-6">
                        <div
                            className="w-12 h-12 rounded-lg flex items-center justify-center"
                            style={{ 
                                backgroundColor: accentColor || theme.colors.primary + '10',
                                color: accentColor || theme.colors.primary
                            }}
                        >
                            {icon}
                        </div>
                    </div>
                )}
                
                <div className="flex-1">
                    <h3
                        className="text-xl font-bold mb-3 group-hover:text-opacity-80 transition-all duration-300"
                        style={{ color: theme.colors.text }}
                    >
                        {title}
                    </h3>
                    
                    <p
                        className="text-base leading-relaxed mb-4"
                        style={{ color: theme.colors.secondaryText }}
                    >
                        {description}
                    </p>
                    
                    {features.length > 0 && (
                        <div className="space-y-2">
                            {features.map((feature, index) => (
                                <div key={index} className="flex items-center space-x-2">
                                    <div
                                        className="w-1.5 h-1.5 rounded-full"
                                        style={{ backgroundColor: accentColor || theme.colors.primary }}
                                    />
                                    <span
                                        className="text-sm"
                                        style={{ color: theme.colors.text }}
                                    >
                                        {feature}
                                    </span>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
                
                {onClick && (
                    <div className="mt-6 pt-4 border-t border-opacity-10" style={{ borderColor: theme.colors.border }}>
                        <motion.div
                            className="flex items-center text-sm font-medium group-hover:translate-x-1 transition-transform duration-300"
                            style={{ color: accentColor || theme.colors.primary }}
                        >
                            Learn More
                            <motion.span
                                className="ml-2"
                                initial={{ x: 0 }}
                                whileHover={{ x: 4 }}
                                transition={{ duration: 0.2 }}
                            >
                                →
                            </motion.span>
                        </motion.div>
                    </div>
                )}
            </div>
        </MinimalCard>
    );
};

// Feature highlight card
interface FeatureCardProps {
    icon: React.ReactNode;
    title: string;
    description: string;
    delay?: number;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
    icon,
    title,
    description,
    delay = 0
}) => {
    const { theme } = useTheme();

    return (
        <motion.div
            className="flex items-start space-x-4"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay, ease: "easeOut" }}
            viewport={{ once: true }}
        >
            <div
                className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mt-1"
                style={{ backgroundColor: theme.colors.primary + '15' }}
            >
                <div style={{ color: theme.colors.primary }}>
                    {icon}
                </div>
            </div>
            <div>
                <h4
                    className="text-lg font-semibold mb-2"
                    style={{ color: theme.colors.text }}
                >
                    {title}
                </h4>
                <p
                    className="text-base leading-relaxed"
                    style={{ color: theme.colors.secondaryText }}
                >
                    {description}
                </p>
            </div>
        </motion.div>
    );
};
