'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/lib/context/ThemeContext';
import { CrefyFloatingOrbs } from './CrefyFloatingOrbs';

interface ParallaxBackgroundProps {
    children?: React.ReactNode;
    orbCount?: number;
    className?: string;
    variant?: 'subtle' | 'normal' | 'intense';
}

export const ParallaxBackground: React.FC<ParallaxBackgroundProps> = ({
    children,
    orbCount = 12,
    className = '',
    variant = 'normal'
}) => {
    const { theme } = useTheme();



    return (
        <div
            className={`relative min-h-screen overflow-hidden ${className}`}
            style={{
                background: `linear-gradient(135deg,
                    ${theme.colors.background} 0%,
                    ${theme.colors.background}f8 20%,
                    ${theme.colors.background}f0 40%,
                    ${theme.colors.background}f8 60%,
                    ${theme.colors.background} 80%,
                    ${theme.colors.background} 100%)`
            }}
        >
            {/* Enhanced Floating Orbs using new component */}
            <CrefyFloatingOrbs orbCount={orbCount} variant={variant} />

            {/* Multi-layer Gradient Overlays for Enhanced Glassmorphism */}
            <div
                className="absolute inset-0 pointer-events-none"
                style={{
                    background: `radial-gradient(ellipse at 30% 20%,
                        ${theme.colors.primary}08 0%,
                        transparent 50%),
                    radial-gradient(ellipse at 70% 80%,
                        ${theme.colors.accent}06 0%,
                        transparent 50%),
                    radial-gradient(circle at 50% 50%,
                        transparent 0%,
                        ${theme.colors.background}15 70%,
                        ${theme.colors.background}25 100%)`
                }}
            />

            {/* Subtle animated gradient overlay */}
            <motion.div
                className="absolute inset-0 pointer-events-none"
                style={{
                    background: `conic-gradient(from 0deg at 50% 50%,
                        ${theme.colors.primary}05,
                        transparent,
                        ${theme.colors.accent}03,
                        transparent,
                        ${theme.colors.primary}05)`,
                    filter: 'blur(100px)',
                }}
                animate={{
                    rotate: [0, 360],
                }}
                transition={{
                    duration: 120,
                    ease: "linear",
                    repeat: Infinity,
                }}
            />

            {/* Content */}
            <div className="relative z-10">
                {children}
            </div>
        </div>
    );
};
