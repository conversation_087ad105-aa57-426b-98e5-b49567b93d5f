'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/lib/context/ThemeContext';
import { GlassmorphicCard } from './GlassmorphicCard';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHeart } from '@fortawesome/free-solid-svg-icons';

interface FooterLink {
    label: string;
    href: string;
}

interface FooterSection {
    title: string;
    links: FooterLink[];
}

interface CrefyFooterProps {
    sections?: FooterSection[];
    showProducts?: boolean;
    className?: string;
}

export const CrefyFooter: React.FC<CrefyFooterProps> = ({
    sections = [],
    showProducts = true,
    className = ''
}) => {
    const { theme } = useTheme();

    return (
        <motion.footer
            className={`p-4 mt-12 ${className}`}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
        >
            <div className="max-w-7xl mx-auto">
                <GlassmorphicCard
                    padding="md"
                    rounded="2xl"
                    className="h-18"
                    style={{
                        backgroundColor: '#FFFFFF',
                        backdropFilter: 'blur(25px)',
                        border: `1px solid rgba(255, 255, 255, 0.8)`,
                        boxShadow: '0 -4px 20px rgba(255, 255, 255, 0.8)',
                    }}
                >
                    {/* Simplified Footer Content */}
                    <div className="flex items-center justify-between h-full">
                        {/* Brand Section */}
                        <div className="flex items-center space-x-3">
                            <div
                                className="w-8 h-8 rounded-lg flex items-center justify-center"
                                style={{ backgroundColor: theme.colors.primary }}
                            >
                                <span className="text-white font-bold text-sm">C</span>
                            </div>
                            <h3
                                className="text-lg font-bold"
                                style={{ color: theme.colors.text }}
                            >
                                Crefy
                            </h3>
                        </div>

                        {/* Right Section */}
                        <div className="flex items-center space-x-6">
                            <div className="flex items-center space-x-2">
                                <span
                                    className="text-sm"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    Built with
                                </span>
                                <FontAwesomeIcon
                                    icon={faHeart}
                                    className="w-3 h-3 text-red-500"
                                />
                                <motion.a
                                    href="https://crefy.xyz"
                                    className="text-sm font-medium transition-colors duration-200"
                                    style={{ color: theme.colors.primary }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    Crefy
                                </motion.a>
                            </div>

                            <span
                                className="text-sm"
                                style={{ color: theme.colors.secondaryText }}
                            >
                                © 2024 Crefy
                            </span>
                        </div>
                    </div>
                </GlassmorphicCard>
            </div>
        </motion.footer>
    );
};
