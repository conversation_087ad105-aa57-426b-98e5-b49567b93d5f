'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { useTheme } from '@/lib/context/ThemeContext';
import { AnimatedText, AnimatedWords, GradientText } from './AnimatedText';
import { CrefyButton } from './CrefyButton';

interface MinimalHeroProps {
    title: string;
    subtitle?: string;
    description: string;
    primaryAction: {
        text: string;
        onClick: () => void;
    };
    secondaryAction?: {
        text: string;
        onClick: () => void;
    };
    scrollTarget?: string;
    className?: string;
}

export const MinimalHero: React.FC<MinimalHeroProps> = ({
    title,
    subtitle,
    description,
    primaryAction,
    secondaryAction,
    scrollTarget,
    className = ''
}) => {
    const { theme } = useTheme();

    const handleScrollToSection = () => {
        if (scrollTarget) {
            const element = document.getElementById(scrollTarget);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        }
    };

    return (
        <section className={`min-h-screen flex items-center justify-center px-4 relative ${className}`}>
            {/* Subtle background elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <motion.div
                    className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full opacity-5"
                    style={{ backgroundColor: theme.colors.primary }}
                    animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.05, 0.1, 0.05]
                    }}
                    transition={{
                        duration: 8,
                        repeat: Infinity,
                        ease: "easeInOut"
                    }}
                />
                <motion.div
                    className="absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full opacity-5"
                    style={{ backgroundColor: theme.colors.accent }}
                    animate={{
                        scale: [1.2, 1, 1.2],
                        opacity: [0.1, 0.05, 0.1]
                    }}
                    transition={{
                        duration: 10,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 2
                    }}
                />
            </div>

            <div className="max-w-5xl mx-auto text-center relative z-10">
                {/* Main Title */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                    className="mb-6"
                >
                    <GradientText
                        text={title}
                        className="text-7xl md:text-8xl lg:text-9xl font-bold leading-tight"
                        gradient={`linear-gradient(135deg, ${theme.colors.text} 0%, ${theme.colors.primary} 50%, ${theme.colors.accent} 100%)`}
                        animate={true}
                    />
                </motion.div>

                {/* Subtitle */}
                {subtitle && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
                        className="mb-8"
                    >
                        <h2
                            className="text-2xl md:text-3xl lg:text-4xl font-semibold"
                            style={{ color: theme.colors.primary }}
                        >
                            {subtitle}
                        </h2>
                    </motion.div>
                )}

                {/* Description */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
                    className="mb-12"
                >
                    <AnimatedWords
                        text={description}
                        className="text-xl md:text-2xl leading-relaxed max-w-4xl mx-auto"
                        style={{ color: theme.colors.secondaryText }}
                        delay={0.6}
                        staggerDelay={0.05}
                    />
                </motion.div>

                {/* Action Buttons */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
                >
                    <CrefyButton
                        variant="primary"
                        size="lg"
                        onClick={primaryAction.onClick}
                        className="min-w-[200px]"
                    >
                        {primaryAction.text}
                    </CrefyButton>
                    
                    {secondaryAction && (
                        <CrefyButton
                            variant="glass"
                            size="lg"
                            onClick={secondaryAction.onClick}
                            className="min-w-[200px]"
                        >
                            {secondaryAction.text}
                        </CrefyButton>
                    )}
                </motion.div>

                {/* Key Features */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.0, ease: "easeOut" }}
                    className="flex flex-wrap justify-center gap-8 text-center"
                >
                    {[
                        { text: 'Secure', icon: '🔒' },
                        { text: 'Decentralized', icon: '🌐' },
                        { text: 'Private', icon: '🛡️' }
                    ].map((feature, index) => (
                        <motion.div
                            key={index}
                            className="flex items-center space-x-3 group"
                            whileHover={{ scale: 1.05 }}
                            transition={{ duration: 0.2 }}
                        >
                            <span className="text-2xl group-hover:scale-110 transition-transform duration-200">
                                {feature.icon}
                            </span>
                            <span
                                className="text-lg font-medium group-hover:opacity-80 transition-opacity duration-200"
                                style={{ color: theme.colors.text }}
                            >
                                {feature.text}
                            </span>
                        </motion.div>
                    ))}
                </motion.div>
            </div>

            {/* Scroll Indicator */}
            {scrollTarget && (
                <motion.div
                    className="absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer"
                    onClick={handleScrollToSection}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.2, ease: "easeOut" }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                >
                    <motion.div
                        className="flex flex-col items-center space-y-2"
                        animate={{ y: [0, 8, 0] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    >
                        <span
                            className="text-sm font-medium"
                            style={{ color: theme.colors.secondaryText }}
                        >
                            Explore
                        </span>
                        <FontAwesomeIcon
                            icon={faChevronDown}
                            className="text-lg"
                            style={{ color: theme.colors.primary }}
                        />
                    </motion.div>
                </motion.div>
            )}
        </section>
    );
};
