'use client';

import React, { useEffect, useState } from 'react';
import { motion, useAnimation } from 'framer-motion';

interface AnimatedTextProps {
    text: string;
    className?: string;
    delay?: number;
    duration?: number;
    variant?: 'typewriter' | 'fadeInUp' | 'slideInLeft' | 'glitch' | 'wave';
    style?: React.CSSProperties;
}

export const AnimatedText: React.FC<AnimatedTextProps> = ({
    text,
    className = '',
    delay = 0,
    duration = 0.05,
    variant = 'fadeInUp',
    style = {}
}) => {
    const [displayText, setDisplayText] = useState('');
    const controls = useAnimation();

    useEffect(() => {
        if (variant === 'typewriter') {
            let currentIndex = 0;
            const timer = setInterval(() => {
                if (currentIndex <= text.length) {
                    setDisplayText(text.slice(0, currentIndex));
                    currentIndex++;
                } else {
                    clearInterval(timer);
                }
            }, duration * 1000);

            return () => clearInterval(timer);
        } else {
            setDisplayText(text);
        }
    }, [text, variant, duration]);

    const getVariantAnimation = () => {
        switch (variant) {
            case 'typewriter':
                return {
                    initial: { opacity: 1 },
                    animate: { opacity: 1 },
                    transition: { delay }
                };
            case 'fadeInUp':
                return {
                    initial: { opacity: 0, y: 50 },
                    animate: { opacity: 1, y: 0 },
                    transition: { duration: 0.8, delay, ease: "easeOut" }
                };
            case 'slideInLeft':
                return {
                    initial: { opacity: 0, x: -100 },
                    animate: { opacity: 1, x: 0 },
                    transition: { duration: 0.8, delay, ease: "easeOut" }
                };
            case 'glitch':
                return {
                    initial: { opacity: 0 },
                    animate: { 
                        opacity: [0, 1, 0.8, 1],
                        x: [0, -2, 2, 0],
                        textShadow: [
                            '0 0 0 transparent',
                            '2px 0 0 #ff0000, -2px 0 0 #00ffff',
                            '0 0 0 transparent'
                        ]
                    },
                    transition: { duration: 0.6, delay, ease: "easeInOut" }
                };
            case 'wave':
                return {
                    initial: { opacity: 0, y: 20 },
                    animate: { opacity: 1, y: 0 },
                    transition: { duration: 0.6, delay, ease: "easeOut" }
                };
            default:
                return {
                    initial: { opacity: 0, y: 20 },
                    animate: { opacity: 1, y: 0 },
                    transition: { duration: 0.6, delay, ease: "easeOut" }
                };
        }
    };

    if (variant === 'wave') {
        return (
            <div className={className} style={style}>
                {text.split('').map((char, index) => (
                    <motion.span
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{
                            duration: 0.5,
                            delay: delay + index * 0.05,
                            ease: "easeOut"
                        }}
                        style={{ display: 'inline-block' }}
                    >
                        {char === ' ' ? '\u00A0' : char}
                    </motion.span>
                ))}
            </div>
        );
    }

    const animation = getVariantAnimation();

    return (
        <motion.div
            className={className}
            style={style}
            initial={animation.initial}
            animate={animation.animate}
            transition={animation.transition}
        >
            {variant === 'typewriter' && (
                <>
                    {displayText}
                    <motion.span
                        animate={{ opacity: [1, 0] }}
                        transition={{ duration: 0.8, repeat: Infinity, repeatType: "reverse" }}
                        className="inline-block w-0.5 h-6 bg-current ml-1"
                    />
                </>
            )}
            {variant !== 'typewriter' && text}
        </motion.div>
    );
};

// Word-by-word animation component
interface AnimatedWordsProps {
    text: string;
    className?: string;
    delay?: number;
    staggerDelay?: number;
    style?: React.CSSProperties;
}

export const AnimatedWords: React.FC<AnimatedWordsProps> = ({
    text,
    className = '',
    delay = 0,
    staggerDelay = 0.1,
    style = {}
}) => {
    const words = text.split(' ');

    return (
        <div className={className} style={style}>
            {words.map((word, index) => (
                <motion.span
                    key={index}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                        duration: 0.6,
                        delay: delay + index * staggerDelay,
                        ease: "easeOut"
                    }}
                    className="inline-block mr-2"
                >
                    {word}
                </motion.span>
            ))}
        </div>
    );
};

// Gradient text animation component
interface GradientTextProps {
    text: string;
    className?: string;
    gradient?: string;
    animate?: boolean;
    style?: React.CSSProperties;
}

export const GradientText: React.FC<GradientTextProps> = ({
    text,
    className = '',
    gradient = 'linear-gradient(45deg, #4B2C6F, #B89CD9, #A78BCA)',
    animate = true,
    style = {}
}) => {
    return (
        <motion.div
            className={className}
            style={{
                background: gradient,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                backgroundSize: animate ? '200% 200%' : '100% 100%',
                ...style
            }}
            animate={animate ? {
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
            } : {}}
            transition={animate ? {
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
            } : {}}
        >
            {text}
        </motion.div>
    );
};
